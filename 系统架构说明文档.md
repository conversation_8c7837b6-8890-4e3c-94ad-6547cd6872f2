
## 项目架构总览

这是一个现代化的全栈股票分析系统，采用前后端分离架构：

### 核心技术栈
- **后端**: Python FastAPI + 异步处理
- **前端**: Vue 3 + TypeScript + Naive UI
- **部署**: Docker + Nginx + GitHub Actions CI/CD

## 主要功能模块

### 1. 股票分析引擎 (`server/services/`) 基于您提供的README文档和我对代码库的深入分析，以下是这个股票分析系统项目的全面分析：

## 项目概述

这是一个现代化的**全栈股票分析系统**，基于原开源项目 https://github.com/DR-lin-eng/stock-scanner 进行二次开发和重构，支持多市场股票技术分析和AI智能分析。

## 核心架构

### 技术栈
- **后端**: Python FastAPI + 异步处理
- **前端**: Vue 3 + TypeScript + Vite + Naive UI
- **部署**: Docker + Nginx + GitHub Actions CI/CD
- **数据库**: SQLite (用户认证)
- **外部API**: 支持OpenAI等大语言模型

## 主要功能模块分析

### 1. 后端服务架构 (`server/`) 基于对整个代码库的深入分析，我为您提供这个股票分析系统项目的全面技术分析：

## 项目架构总览

这是一个现代化的**全栈股票分析系统**，采用前后端分离架构，支持多市场股票技术分析和AI智能分析。

### 核心技术栈
- **后端**: Python FastAPI + 异步处理
- **前端**: Vue 3 + TypeScript + Vite + Naive UI  
- **部署**: Docker + Nginx + GitHub Actions CI/CD
- **数据库**: SQLite (用户认证)
- **外部API**: 支持OpenAI等大语言模型

## 详细模块分析

### 1. 后端服务架构 (`server/`) 基于对整个代码库的深入分析，我为您提供这个股票分析系统项目的全面技术分析：

## 项目概述

这是一个现代化的**全栈股票分析系统**，基于开源项目进行二次开发，支持多市场股票技术分析和AI智能分析。

## 核心架构分析

### 1. 后端服务架构 (`server/`) 我将为您分析这个股票扫描器项目的代码库结构和功能。让我先深入了解项目的整体架构。 现在让我查看更多关键的项目文件： 基于我对这个股票扫描器项目代码库的深入分析，我来为您提供一个全面的项目分析报告：

# 📊 股票分析系统 (Stock Scanner) 项目分析报告

## 🏗️ 项目架构概览

这是一个**现代化的股票分析系统**，采用前后端分离的微服务架构，具有以下特点：

### 核心架构
- **前端**: Vue 3 + TypeScript + Vite + Naive UI
- **后端**: Python FastAPI + 异步编程模式
- **部署**: Docker容器化 + Nginx反向代理
- **数据源**: AkShare金融数据库
- **AI集成**: 支持多种LLM API (OpenAI, Claude, Gemini等)

## 📁 项目结构分析

```
stock-scanner/
├── frontend/          # Vue3前端应用
│   ├── src/
│   │   ├── components/    # 核心组件
│   │   ├── services/     # API服务层
│   │   ├── types/        # TypeScript类型定义
│   │   └── utils/        # 工具函数
├── server/            # Python后端服务
│   ├── services/         # 业务服务层
│   ├── models/          # 数据模型
│   ├── utils/           # 工具类
│   └── tests/           # 测试文件
├── nginx/             # Nginx配置
└── docker配置文件
```

## 🔧 技术栈详解

### 后端技术栈
- **核心框架**: FastAPI (0.115.12) - 现代异步Web框架
- **数据处理**: Pandas + NumPy - 科学计算和数据分析
- **数据源**: AkShare (1.16.84) - 中国股市数据获取
- **异步处理**: httpx + AsyncGenerator - 流式响应处理
- **安全认证**: JWT + OAuth2 + bcrypt密码加密
- **数据库**: PostgreSQL + SQLAlchemy异步ORM
- **限流保护**: SlowAPI - API频率限制

### 前端技术栈
- **框架**: Vue 3.5.13 + Composition API
- **构建工具**: Vite 6.2.0 - 快速开发构建
- **UI组件**: Naive UI 2.41.0 - 现代Vue组件库
- **路由**: Vue Router 4.5.0
- **HTTP客户端**: Axios 1.8.1
- **工具库**: VueUse 12.8.2 - Vue组合式API工具集

## 🚀 核心功能模块

### 1. 股票分析引擎
**文件**: stock_analyzer_service.py
- 技术指标计算（RSI、MACD、布林带、移动平均线等）
- 综合评分算法
- 多市场支持（A股、港股、美股、ETF/LOF基金）

### 2. AI智能分析
**文件**: ai_analyzer.py
- 支持多种LLM API（OpenAI、Claude、Gemini、DeepSeek等）
- 流式响应处理
- 智能API URL格式化
- 自适应JSON解析

### 3. 数据提供服务
**文件**: stock_data_provider.py
- AkShare数据获取
- 异步数据处理
- 多市场数据标准化

### 4. Web API层
**文件**: web_server.py
- RESTful API设计
- JWT认证系统
- 流式响应支持
- API频率限制
- CORS跨域处理

### 5. 前端用户界面
**文件**: StockAnalysisApp.vue
- 响应式设计
- 实时流式数据显示
- 股票搜索和分析
- API配置管理

## 🔍 技术指标体系

系统实现了完整的技术分析指标：

### 趋势指标
- **移动平均线**: MA5、MA20、MA60
- **指数移动平均**: EMA计算
- **MACD**: 12-26-9参数配置

### 动量指标
- **RSI**: 14日相对强弱指标
- **随机指标**: %K和%D线

### 波动性指标
- **布林带**: 20日均线±2标准差
- **ATR**: 平均真实波幅

### 成交量指标
- **OBV**: 能量潮指标
- **成交量比率**: 放量缩量判断

## 🔐 安全机制

### 认证授权
- **JWT令牌**: 7天有效期
- **密码加密**: bcrypt哈希
- **可选登录**: 环境变量控制

### API保护
- **频率限制**: 每IP每分钟5次请求
- **CORS防护**: 跨域请求控制
- **输入验证**: Pydantic数据验证

## 📊 数据流架构

### 分析流程
1. **数据获取**: AkShare API → 原始股票数据
2. **数据清洗**: 标准化列名和数据格式
3. **指标计算**: 技术指标引擎处理
4. **AI分析**: LLM API智能分析
5. **结果展示**: 流式响应前端显示

### 异步处理
- **数据获取**: `asyncio.to_thread`包装同步调用
- **批量处理**: 并发分析多只股票
- **流式响应**: `AsyncGenerator`实时传输

## 🐳 容器化部署

### Docker多阶段构建
1. **前端构建**: Node.js环境编译Vue应用
2. **后端依赖**: Python环境安装依赖包
3. **运行环境**: 轻量级Python镜像

### 服务编排
- **应用容器**: 8888端口提供API服务
- **Nginx代理**: 80/443端口处理HTTP/HTTPS
- **数据库**: PostgreSQL持久化存储
- **网络隔离**: Docker内部网络通信

## 📈 性能优化

### 缓存策略
- **基金数据缓存**: 30分钟有效期
- **前端配置缓存**: localStorage本地存储

### 并发处理
- **多线程分析**: ThreadPoolExecutor批量处理
- **异步I/O**: 非阻塞数据获取
- **流式传输**: 减少内存占用

## 🔧 开发工具集成

### CI/CD
- **GitHub Actions**: 自动化构建部署
- **多架构支持**: x86_64和ARM64镜像

### 测试框架
- **API测试**: test_akshare_apis.py
- **流式测试**: test_stream.py
- **端到端测试**: Playwright集成

## 💡 项目亮点

### 1. 架构设计
- **微服务化**: 服务层清晰分离
- **异步优先**: 全面使用async/await
- **类型安全**: TypeScript + Pydantic双重保障

### 2. 用户体验
- **实时反馈**: 流式数据传输
- **响应式界面**: 移动端适配
- **智能配置**: API参数自动格式化

### 3. 扩展性
- **多市场支持**: A股/港股/美股统一接口
- **AI模型灵活**: 支持多种LLM提供商
- **部署友好**: 一键Docker部署

### 4. 安全性
- **认证机制**: JWT + 密码加密
- **访问控制**: API频率限制
- **配置安全**: 环境变量管理

## 🚀 推荐优化方向

1. **性能优化**: 添加Redis缓存层
2. **监控告警**: 集成Prometheus + Grafana
3. **数据扩展**: 支持更多数据源
4. **AI增强**: 集成更多分析模型
5. **测试覆盖**: 增加单元测试和集成测试

这个项目展现了现代Web应用的优秀架构设计，结合了金融数据分析、AI智能和现代Web技术，是一个非常完整和专业的股票分析系统。