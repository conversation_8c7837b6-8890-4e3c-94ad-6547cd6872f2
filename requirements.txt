# 本地打包使用国内镜像源加速下载， 默认不使用，影响github actions 
# --index-url https://pypi.tuna.tsinghua.edu.cn/simple

# 基础科学计算和数据处理库
numpy==2.2.5
pandas==2.2.3
scipy==1.15.2

# 数据获取和分析库
akshare==1.16.84
tqdm==4.66.4

# Web框架与异步处理
fastapi==0.115.12
uvicorn[standard]==0.34.2
pydantic==2.11.3
httpx==0.28.1

# 环境配置
python-dotenv==1.0.1

# 日志和系统工具
loguru==0.7.2

# 可选：数据可视化（未来扩展）
matplotlib==3.8.4
seaborn==0.13.2

# 开发和调试工具
ipython>=8.18.1

# 其他依赖
beautifulsoup4==4.12.3
html5lib==1.1
lxml==5.4.0
jsonpath-ng==1.6.0
openpyxl==3.1.5
python-jose[cryptography]==3.4.0
passlib>=1.7.4
sqlalchemy[asyncio]
asyncpg
passlib[bcrypt]
slowapi>=0.1.7
bcrypt==3.2.2
ratelimit==2.2.1
