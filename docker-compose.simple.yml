# 用于直接从DockerHub拉取镜像部署

version: '3.8'

services:
  app:
    image: cassianvale/stock-scanner:latest
    container_name: stock-scanner-app
    ports:
      - "8888:8888"
    environment:
      - API_KEY=${API_KEY}
      - API_URL=${API_URL}
      - API_MODEL=${API_MODEL}
      - API_TIMEOUT=${API_TIMEOUT}
      - LOGIN_PASSWORD=${LOGIN_PASSWORD}
      - ANNOUNCEMENT_TEXT=${ANNOUNCEMENT_TEXT}
      - DATABASE_URL=postgresql+asyncpg://postgres:${POSTGRES_PASSWORD}@db:5432/stock_scanner
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    networks:
      - stock-scanner-network
    depends_on:
      - db

  nginx:
    image: nginx:stable-alpine
    container_name: stock-scanner-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/logs:/var/log/nginx
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
    networks:
      - stock-scanner-network

  db:
    image: postgres:15
    container_name: stock-scanner-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=stock_scanner
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - stock-scanner-network

networks:
  stock-scanner-network:
    driver: bridge

volumes:
  pgdata: 