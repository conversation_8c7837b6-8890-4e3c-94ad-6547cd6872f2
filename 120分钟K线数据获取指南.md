# 📊 120分钟K线数据获取指南

## 🎯 功能概述

项目现已支持获取**120分钟（2小时）K线数据**，通过合成60分钟数据实现。

## ✅ 支持的时间周期

- **1分钟、5分钟、15分钟、30分钟、60分钟**：AkShare原生支持
- **120分钟**：通过60分钟数据重采样合成

## 🚀 使用方法

### 1. 异步方式获取

```python
from server.services.stock_data_provider import StockDataProvider

# 创建数据提供者实例
provider = StockDataProvider()

# 获取120分钟K线数据
df = await provider.get_stock_minute_data(
    stock_code="000001",  # 股票代码
    period="120",         # 120分钟周期
    start_date="2023-12-01 09:30:00",  # 开始时间
    end_date="2023-12-10 15:00:00",    # 结束时间
    market_type="A"       # A股市场
)

print(f"获取到 {len(df)} 条120分钟K线数据")
print(df.head())
```

### 2. 其他分钟周期示例

```python
# 获取60分钟数据
df_60min = await provider.get_stock_minute_data(
    stock_code="000001",
    period="60"
)

# 获取30分钟数据
df_30min = await provider.get_stock_minute_data(
    stock_code="000001", 
    period="30"
)

# 获取15分钟数据
df_15min = await provider.get_stock_minute_data(
    stock_code="000001",
    period="15"
)
```

## 📋 数据格式

返回的DataFrame包含以下列：

| 列名 | 类型 | 说明 |
|------|------|------|
| datetime | DatetimeIndex | 时间索引 |
| open | float | 开盘价 |
| high | float | 最高价 |
| low | float | 最低价 |
| close | float | 收盘价 |
| volume | float | 成交量 |
| amount | float | 成交额 |

## ⚠️ 注意事项

### 1. 市场支持
- **目前仅支持A股市场**（market_type='A'）
- 港股和美股暂不支持分钟级别数据

### 2. 数据限制
- **历史数据时间段较短**：AkShare分钟数据通常只提供近期数据
- **建议获取范围**：最近7-30天的数据

### 3. 120分钟数据说明
- **合成方式**：先获取60分钟数据，然后每2小时合并
- **开盘价**：取2小时内第一个60分钟K线的开盘价
- **收盘价**：取2小时内最后一个60分钟K线的收盘价  
- **最高价/最低价**：取2小时内所有60分钟K线的最高/最低价
- **成交量/成交额**：2小时内所有60分钟K线的总和

### 4. 错误处理
```python
# 检查是否有错误
if hasattr(df, 'error'):
    print(f"获取数据失败: {df.error}")
else:
    print("数据获取成功")
```

## 🔧 技术实现

### 重采样算法
120分钟K线通过pandas的`resample('2H')`方法实现：

```python
agg_dict = {
    'open': 'first',    # 开盘价取第一个
    'high': 'max',      # 最高价取最大值
    'low': 'min',       # 最低价取最小值  
    'close': 'last',    # 收盘价取最后一个
    'volume': 'sum',    # 成交量求和
    'amount': 'sum'     # 成交额求和
}
df_120min = df.resample('2H').agg(agg_dict)
```

## 📈 应用场景

### 1. 技术分析
- **中期趋势分析**：2小时周期适合捕捉中期价格趋势
- **支撑阻力位**：更平滑的价格曲线，减少噪音
- **移动平均线**：120分钟MA比60分钟MA更稳定

### 2. 量化策略
- **趋势跟踪策略**：使用120分钟数据判断中期趋势方向
- **突破策略**：基于120分钟高低点的突破信号
- **均值回归策略**：120分钟级别的价格偏离修正

### 3. 风险管理
- **止损设置**：基于120分钟支撑阻力位设置止损
- **仓位管理**：根据120分钟趋势强度调整仓位

## 🎯 最佳实践

### 1. 数据获取建议
```python
# 推荐的时间范围设置
start_date = (datetime.now() - timedelta(days=7)).strftime('%Y-%m-%d %H:%M:%S')
end_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

df = await provider.get_stock_minute_data(
    stock_code="000001",
    period="120",
    start_date=start_date,
    end_date=end_date
)
```

### 2. 性能优化
- **批量获取**：避免频繁调用API
- **缓存策略**：对已获取的数据进行本地缓存
- **异步处理**：使用asyncio并发获取多只股票数据

### 3. 数据质量检查
```python
# 检查数据完整性
if not df.empty:
    print(f"数据时间范围: {df.index.min()} 到 {df.index.max()}")
    print(f"数据点数: {len(df)}")
    print(f"缺失值: {df.isnull().sum().sum()}")
else:
    print("数据为空，请检查股票代码和时间范围")
```

## 🔄 未来扩展

### 1. 支持更多市场
- 计划增加港股和美股的分钟级别数据支持
- 期货、期权等衍生品分钟数据

### 2. 更多时间周期
- 240分钟（4小时）
- 自定义时间周期

### 3. 实时数据
- WebSocket实时分钟数据推送
- 与历史数据的无缝对接

---

> **更新时间**：2025-06-21  
> **版本**：v1.0  
> **状态**：✅ 已实现并测试
