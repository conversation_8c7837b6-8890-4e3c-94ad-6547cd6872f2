# 📊 240分钟K线数据获取指南

## 🎯 功能概述

项目现已支持获取**240分钟（4小时）K线数据**，通过合成60分钟数据实现。这为中长期技术分析和量化策略提供了更多选择。

## ✅ 支持的时间周期

- **1分钟、5分钟、15分钟、30分钟、60分钟**：AkShare原生支持
- **120分钟**：通过60分钟数据重采样合成（2小时）
- **240分钟**：通过60分钟数据重采样合成（4小时）⭐ **新增**

## 🚀 使用方法

### 1. 异步方式获取240分钟数据

```python
from server.services.stock_data_provider import StockDataProvider

# 创建数据提供者实例
provider = StockDataProvider()

# 获取240分钟K线数据
df = await provider.get_stock_minute_data(
    stock_code="000001",  # 股票代码
    period="240",         # 240分钟周期（4小时）
    start_date="2023-12-01 09:30:00",  # 开始时间
    end_date="2023-12-10 15:00:00",    # 结束时间
    market_type="A"       # A股市场
)

print(f"获取到 {len(df)} 条240分钟K线数据")
print(df.head())
```

### 2. 与其他周期对比

```python
# 获取不同周期数据进行对比
periods = ["60", "120", "240"]

for period in periods:
    df = await provider.get_stock_minute_data(
        stock_code="000001",
        period=period
    )
    print(f"{period}分钟数据: {len(df)} 条记录")
```

### 3. 批量获取示例

```python
# 批量获取多只股票的240分钟数据
stock_codes = ["000001", "000002", "600036", "600519"]

for code in stock_codes:
    df = await provider.get_stock_minute_data(
        stock_code=code,
        period="240"
    )
    if not hasattr(df, 'error') and not df.empty:
        print(f"{code}: {len(df)} 条240分钟数据")
```

## 📋 数据格式

返回的DataFrame包含以下列：

| 列名 | 类型 | 说明 |
|------|------|------|
| datetime | DatetimeIndex | 时间索引（4小时间隔） |
| open | float | 开盘价 |
| high | float | 最高价 |
| low | float | 最低价 |
| close | float | 收盘价 |
| volume | float | 成交量（4小时累计） |
| amount | float | 成交额（4小时累计） |

## ⚠️ 注意事项

### 1. 市场支持
- **目前仅支持A股市场**（market_type='A'）
- 港股和美股暂不支持分钟级别数据

### 2. 数据限制
- **历史数据时间段较短**：AkShare分钟数据通常只提供近期数据
- **建议获取范围**：最近7-30天的数据

### 3. 240分钟数据说明
- **合成方式**：先获取60分钟数据，然后每4小时合并
- **开盘价**：取4小时内第一个60分钟K线的开盘价
- **收盘价**：取4小时内最后一个60分钟K线的收盘价  
- **最高价/最低价**：取4小时内所有60分钟K线的最高/最低价
- **成交量/成交额**：4小时内所有60分钟K线的总和

### 4. 错误处理
```python
# 检查是否有错误
if hasattr(df, 'error'):
    print(f"获取数据失败: {df.error}")
else:
    print("数据获取成功")
```

## 🔧 技术实现

### 重采样算法
240分钟K线通过pandas的`resample('4H')`方法实现：

```python
agg_dict = {
    'open': 'first',    # 开盘价取第一个
    'high': 'max',      # 最高价取最大值
    'low': 'min',       # 最低价取最小值  
    'close': 'last',    # 收盘价取最后一个
    'volume': 'sum',    # 成交量求和
    'amount': 'sum'     # 成交额求和
}
df_240min = df.resample('4H').agg(agg_dict)
```

这种方法确保了数据的准确性和一致性。

## 📈 应用场景

### 1. 技术分析
- **长期趋势分析**：4小时周期适合捕捉中长期价格趋势
- **关键价位识别**：更清晰的支撑阻力位
- **移动平均线**：240分钟MA比短周期MA更稳定，噪音更少

### 2. 量化策略
- **趋势跟踪策略**：使用240分钟数据判断长期趋势方向
- **突破策略**：基于240分钟高低点的重要突破信号
- **波段交易策略**：240分钟级别的波段操作信号

### 3. 风险管理
- **止损设置**：基于240分钟重要支撑阻力位设置止损
- **仓位管理**：根据240分钟趋势强度调整仓位大小
- **市场节奏把握**：识别市场的中期节奏变化

### 4. 组合分析
- **多周期共振**：结合60分钟、120分钟、240分钟多周期分析  
- **背离识别**：不同周期间的价格背离信号
- **确认信号**：长周期确认短周期的交易信号

## 🎯 最佳实践

### 1. 数据获取建议
```python
# 推荐的时间范围设置
start_date = (datetime.now() - timedelta(days=15)).strftime('%Y-%m-%d %H:%M:%S')
end_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

df = await provider.get_stock_minute_data(
    stock_code="000001",
    period="240",
    start_date=start_date,
    end_date=end_date
)
```

### 2. 性能优化
- **合理的时间范围**：避免获取过长历史数据
- **缓存策略**：对已获取的数据进行本地缓存
- **异步处理**：使用asyncio并发获取多只股票数据

### 3. 数据质量检查
```python
# 检查数据完整性
if not df.empty:
    print(f"数据时间范围: {df.index.min()} 到 {df.index.max()}")
    print(f"数据点数: {len(df)}")
    print(f"缺失值: {df.isnull().sum().sum()}")
    
    # 检查数据连续性
    time_diff = df.index.to_series().diff()
    expected_interval = pd.Timedelta(hours=4)
    irregular_gaps = time_diff[time_diff != expected_interval].dropna()
    
    if len(irregular_gaps) > 0:
        print(f"⚠️ 发现 {len(irregular_gaps)} 个时间间隔异常")
else:
    print("数据为空，请检查股票代码和时间范围")
```

## 📊 使用示例

运行项目中的示例脚本：

```bash
python 240分钟K线数据使用示例.py
```

该脚本将展示：
- 如何获取240分钟K线数据
- 数据格式和统计信息
- 与其他周期的对比分析
- 实际应用场景演示

## 🔄 未来扩展

### 1. 更多自定义周期
- 支持任意小时数的自定义周期
- 支持跨日周期合成

### 2. 实时数据
- WebSocket实时240分钟数据推送
- 与历史数据的无缝对接

### 3. 更多市场支持
- 港股和美股的240分钟数据支持
- 期货、期权等衍生品240分钟数据

---

> **更新时间**：2025-06-21  
> **版本**：v1.1  
> **状态**：✅ 已实现并可用
> **新增功能**：240分钟K线数据支持
