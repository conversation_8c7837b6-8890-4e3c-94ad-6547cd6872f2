# 本地开发和手动构建场景

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: stock-scanner-app
    ports:
      - "8888:8888"
    environment:
      - API_KEY=${API_KEY}
      - API_URL=${API_URL}
      - API_MODEL=${API_MODEL}
      - API_TIMEOUT=${API_TIMEOUT}
      - LOGIN_PASSWORD=${LOGIN_PASSWORD}
      - ANNOUNCEMENT_TEXT=${ANNOUNCEMENT_TEXT}
      - DATABASE_URL=postgresql+asyncpg://postgres:${POSTGRES_PASSWORD}@db:5432/stock_scanner
      - TZ=Asia/Shanghai
    volumes:
      - ./logs:/app/logs
      - ./server:/app/server
      - ./frontend/src:/app/frontend/src
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8888/api/config"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    depends_on:
      - db
    networks:
      - stock-scanner-network

  nginx:
    image: nginx:alpine
    container_name: stock-scanner-nginx
    ports:
      - "8080:80"
      - "9443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/conf.d/default.conf
      - ./nginx/logs:/var/log/nginx
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    networks:
      - stock-scanner-network

  db:
    image: postgres:15
    container_name: stock-scanner-db
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=stock_scanner
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - stock-scanner-network

networks:
  stock-scanner-network:
    driver: bridge

volumes:
  pgdata:
