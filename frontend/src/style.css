:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background-color: #f5f5f7;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(32, 128, 240, 0.2) !important;
}

.card {
  padding: 2em;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  width: 100%;
  box-sizing: border-box;
  text-align: center;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* 全局移除黑色边框 */
*:focus {
  outline: none !important;
}

/* 输入框和表单元素的焦点样式 */
input:focus,
textarea:focus,
select:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(32, 128, 240, 0.2) !important;
}

/* 移除移动设备上的点击高亮 */
* {
  -webkit-tap-highlight-color: transparent;
}

/* 修复Firefox特定的焦点样式 */
button::-moz-focus-inner,
input::-moz-focus-inner {
  border: 0;
}

/* Naive UI组件的焦点样式 */
.n-button:focus,
.n-button:focus-visible,
.n-input:focus-within,
.n-input-number:focus-within,
.n-select:focus-within,
.n-tag:focus {
  outline: none !important;
  box-shadow: 0 0 0 2px rgba(32, 128, 240, 0.2) !important;
}
/* 组件宽度统一规范 */
.component-container {
  width: 100%;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}

.form-container {
  width: 100%;
  max-width: 900px;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}

.control-container {
  width: 100%;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  box-sizing: border-box;
}

/* 移动端适配 */
@media (max-width: 768px) {
  body {
    background-color: #f5f5f7;
    overflow-x: hidden;
    padding: 0;
    margin: 0;
  }
  
  #app {
    width: 100%;
    overflow-x: hidden;
    padding: 1rem;
    margin: 0 auto;
  }
  
  .component-container {
    width: calc(100% - 20px);
    max-width: 560px;
  }
  
  .form-container {
    width: calc(100% - 20px);
    max-width: 480px;
  }
  
  .control-container {
    width: calc(100% - 16px);
    max-width: 440px;
  }
}

@media (max-width: 480px) {
  #app {
    padding: 0.5rem;
  }
  
  .component-container {
    width: calc(100% - 16px);
    max-width: 360px;
  }
  
  .form-container {
    width: calc(100% - 12px);
    max-width: 320px;
  }
  
  .control-container {
    width: calc(100% - 8px);
    max-width: 300px;
  }
}

/* 表单控件统一样式 */
.uniform-input,
.uniform-select,
.uniform-button {
  width: 100%;
  box-sizing: border-box;
  border-radius: 0.5rem;
}

.button-group {
  display: flex;
  gap: 0.5rem;
  width: 100%;
  justify-content: center;
}

