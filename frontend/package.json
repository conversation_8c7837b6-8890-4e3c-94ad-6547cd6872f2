{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@types/node": "^22.13.9", "@vicons/ionicons5": "^0.13.0", "@vueuse/core": "^12.8.2", "axios": "^1.8.1", "marked": "^15.0.7", "naive-ui": "^2.41.0", "npm": "^10.8.2", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@types/marked": "^5.0.2", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}